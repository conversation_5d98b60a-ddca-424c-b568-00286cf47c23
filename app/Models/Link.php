<?php

namespace App\Models;

use App\History\MyLogsActivity;
use App\Observers\LinkObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Attributes\Scope;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Concerns\HasTimestamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * Link Model
 *
 * Represents shortened links in the URL shortener application.
 * Links can be password-protected, have availability windows, and support
 * various configuration options for query parameter handling.
 *
 * @property-read bool $is_available Whether the link is currently available
 * @property-read bool $has_password Whether the link has password protection
 *
 * @see \App\Observers\LinkObserver
 * @see \App\Services\VisitService
 */
#[ObservedBy([LinkObserver::class])]
class Link extends Model
{
    use HasFactory, HasTimestamps, LogsActivity, MyLogsActivity;

    /** @var array<string> Attributes that cannot be mass assigned */
    protected $guarded = [
        'short_path', // This is auto-generated by the LinkObserver
    ];

    /**
     * Scope a query to only include available links.
     *
     * A link is considered available if:
     * - It is active
     * - It has no availability start date, or the start date has passed
     * - It has no availability end date, or the end date has not passed
     * - It has at least one available domain
     *
     * @param  Builder  $query  The query builder instance
     * @return Builder The modified query builder
     */
    #[Scope]
    protected function available(Builder $query): Builder
    {
        return $query
            ->where('is_active', true)
            ->where(function ($query) {
                $query
                    ->whereNull('available_at')
                    ->orWhere('available_at', '<=', now());
            })
            ->where(function ($query) {
                $query
                    ->whereNull('unavailable_at')
                    ->orWhere('unavailable_at', '>=', now());
            })
            ->whereHas('availableDomains');
    }

    /**
     * Scope a query to only include password-protected links.
     *
     * @param  Builder  $query  The query builder instance
     * @return Builder The modified query builder
     */
    #[Scope]
    protected function passwordProtected(Builder $query): Builder
    {
        return $query->whereNotNull('password');
    }

    /**
     * Scope a query to only include links for the current domain.
     *
     * Uses the current_domain() helper to filter links by domain.
     * If no current domain is set, returns an empty result set.
     *
     * @param  Builder  $query  The query builder instance
     * @return Builder The modified query builder
     */
    #[Scope]
    protected function forCurrentDomain(Builder $query): Builder
    {
        $currentDomain = current_domain();

        if (! $currentDomain?->is_active) {
            return $query->whereRaw('1 = 0'); // Forces an empty result set
        }

        return $query->whereHas('domains', function (Builder $query) use ($currentDomain) {
            $query->where('domains.id', $currentDomain->id);
        });
    }

    /**
     * Determine if the link is currently available.
     *
     * Checks all availability conditions including active status,
     * availability window, and domain availability.
     *
     * @return Attribute<bool, never> The availability status
     */
    public function isAvailable(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->is_active && ($this->available_at === null || $this->available_at <= now()) && ($this->unavailable_at === null || $this->unavailable_at >= now()) && $this->availableDomains()->exists(),
        );
    }

    /**
     * Determine if the link has password protection.
     *
     * @return Attribute<bool, never> Whether the link has a password
     */
    public function hasPassword(): Attribute
    {
        return Attribute::make(
            get: fn () => isset($this->password),
        );
    }

    /**
     * Get the visits associated with this link.
     *
     * @return HasMany<Visit> The visits relationship
     */
    public function visits(): HasMany
    {
        return $this->hasMany(Visit::class, 'link_id');
    }

    /**
     * Get the tags associated with this link.
     *
     * @return BelongsToMany<Tag> The tags relationship
     */
    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class, LinkTag::class);
    }

    /**
     * Get the domains associated with this link.
     *
     * @return BelongsToMany<Domain> The domains relationship
     */
    public function domains(): BelongsToMany
    {
        return $this->belongsToMany(Domain::class, LinkDomain::class);
    }

    /**
     * Get only the active domains associated with this link.
     *
     * @return BelongsToMany<Domain> The available domains relationship
     */
    public function availableDomains(): BelongsToMany
    {
        return $this->domains()
            ->where('is_active', true);
    }

    public function __toString()
    {
        return $this->short_path;
    }

    protected function casts(): array
    {
        return [
            'available_at' => 'datetime',
            'unavailable_at' => 'datetime',
            'forward_query_parameters' => 'boolean',
            'is_active' => 'boolean',
        ];
    }
}
