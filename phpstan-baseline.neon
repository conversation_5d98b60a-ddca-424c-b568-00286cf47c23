parameters:
	ignoreErrors:
		-
			message: '#^Cannot cast mixed to string\.$#'
			identifier: cast.string
			count: 2
			path: app/Console/Commands/MakeSuperAdmin.php

		-
			message: '#^Call to an undefined method Filament\\Tables\\Columns\\TextColumn\:\:numericAbbreviate\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Filament/Admin/Widgets/MostVisitedLinks.php

		-
			message: '#^Cannot call method tooltip\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Admin/Widgets/MostVisitedLinks.php

		-
			message: '#^Parameter \#1 \$components of method Filament\\Tables\\Table\:\:columns\(\) expects array\<Filament\\Tables\\Columns\\Column\|Filament\\Tables\\Columns\\ColumnGroup\|Filament\\Tables\\Columns\\Layout\\Component\>, array\<int, mixed\> given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Admin/Widgets/MostVisitedLinks.php

		-
			message: '#^Using nullsafe method call on non\-nullable type App\\Models\\User\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Filament/Admin/Widgets/MostVisitedLinks.php

		-
			message: '#^Access to an undefined property App\\Filament\\Admin\\Widgets\\QuickLinkCreator\:\:\$form\.$#'
			identifier: property.notFound
			count: 5
			path: app/Filament/Admin/Widgets/QuickLinkCreator.php

		-
			message: '#^Cannot access offset ''original_url'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 2
			path: app/Filament/Admin/Widgets/QuickLinkCreator.php

		-
			message: '#^Cannot call method fill\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 3
			path: app/Filament/Admin/Widgets/QuickLinkCreator.php

		-
			message: '#^Cannot call method getState\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 2
			path: app/Filament/Admin/Widgets/QuickLinkCreator.php

		-
			message: '#^Anonymous function never returns null so it can be removed from the return type\.$#'
			identifier: return.unusedType
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Anonymous function should return string but returns mixed\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Anonymous function should return string\|null but returns mixed\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access offset ''attributes'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 6
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access offset ''old'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 6
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access offset \(int\|string\) on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 2
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access property \$causer on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access property \$changes on mixed\.$#'
			identifier: property.nonObject
			count: 8
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access property \$created_at on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access property \$event on mixed\.$#'
			identifier: property.nonObject
			count: 7
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access property \$name on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot access property \$subject_type on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Cannot call method format\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Method App\\Filament\\Admin\\Widgets\\RecentActivity\:\:getChangesSummary\(\) has parameter \$record with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Method App\\Filament\\Admin\\Widgets\\RecentActivity\:\:getChangesSummaryText\(\) has parameter \$record with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Parameter \#1 \$class of function class_basename expects object\|string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Parameter \#1 \$string of function ucfirst expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Parameter \#1 \$value of function collect expects Illuminate\\Contracts\\Support\\Arrayable\<\(int\|string\), mixed\>\|iterable\<\(int\|string\), mixed\>\|null, mixed given\.$#'
			identifier: argument.type
			count: 2
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Part \$newValue \(mixed\) of encapsed string cannot be cast to string\.$#'
			identifier: encapsedStringPart.nonString
			count: 2
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Part \$oldValue \(mixed\) of encapsed string cannot be cast to string\.$#'
			identifier: encapsedStringPart.nonString
			count: 2
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Unable to resolve the template type TKey in call to function collect$#'
			identifier: argument.templateType
			count: 2
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Unable to resolve the template type TValue in call to function collect$#'
			identifier: argument.templateType
			count: 2
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Using nullsafe property access "\?\-\>name" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/Filament/Admin/Widgets/RecentActivity.php

		-
			message: '#^Method App\\Filament\\Admin\\Widgets\\TopCountriesChart\:\:generateColorPalette\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Admin/Widgets/TopCountriesChart.php

		-
			message: '#^Method App\\Filament\\Admin\\Widgets\\TopCountriesChart\:\:getDateRange\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Admin/Widgets/TopCountriesChart.php

		-
			message: '#^Parameter \#1 \$limit of method Illuminate\\Support\\Collection\<int,App\\Models\\Visit\>\:\:take\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Admin/Widgets/TopCountriesChart.php

		-
			message: '#^Parameter \#1 \$offset of method Illuminate\\Support\\Collection\<int,App\\Models\\Visit\>\:\:slice\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Admin/Widgets/TopCountriesChart.php

		-
			message: '#^Parameter \#1 \.\.\.\$values of method Illuminate\\Support\\Collection\<int,App\\Models\\Visit\>\:\:push\(\) expects App\\Models\\Visit, object\{country\: string, total\: mixed\}&stdClass given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Admin/Widgets/TopCountriesChart.php

		-
			message: '#^Method App\\Filament\\Admin\\Widgets\\VisitsChart\:\:getDateRange\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Admin/Widgets/VisitsChart.php

		-
			message: '#^Method App\\Filament\\Admin\\Widgets\\VisitsChart\:\:getOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Admin/Widgets/VisitsChart.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:map\(\) expects callable\(mixed, int\|string\)\: mixed, Closure\(Flowframe\\Trend\\TrendValue\)\: mixed given\.$#'
			identifier: argument.type
			count: 2
			path: app/Filament/Admin/Widgets/VisitsChart.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:map\(\) expects callable\(mixed, int\|string\)\: string, Closure\(Flowframe\\Trend\\TrendValue\)\: string given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Admin/Widgets/VisitsChart.php

		-
			message: '#^Cannot access property \$id on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Domains/Pages/EditDomain.php

		-
			message: '#^Negated boolean expression is always true\.$#'
			identifier: booleanNot.alwaysTrue
			count: 1
			path: app/Filament/Resources/Domains/Pages/EditDomain.php

		-
			message: '#^Offset ''is_admin_panel_active'' might not exist on array\<string, mixed\>\|null\.$#'
			identifier: offsetAccess.notFound
			count: 1
			path: app/Filament/Resources/Domains/Pages/EditDomain.php

		-
			message: '#^Call to an undefined method Filament\\Tables\\Columns\\TextColumn\:\:numericAbbreviate\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Cannot access property \$is_available on mixed\.$#'
			identifier: property.nonObject
			count: 3
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Cannot access property \$original_url on mixed\.$#'
			identifier: property.nonObject
			count: 2
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Cannot access property \$visit_count on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Cannot call method tooltip\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\LinkResource\:\:getShortUrl\(\) should return string but returns string\|null\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Parameter \#1 \$components of method Filament\\Tables\\Table\:\:columns\(\) expects array\<Filament\\Tables\\Columns\\Column\|Filament\\Tables\\Columns\\ColumnGroup\|Filament\\Tables\\Columns\\Layout\\Component\>, array\<int, mixed\> given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Parameter \#1 \$link of function get_short_url expects App\\Models\\Link, mixed given\.$#'
			identifier: argument.type
			count: 2
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Parameter \#1 \$num of function number_format expects float, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Parameter \#2 \$domain of function get_short_url expects App\\Models\\Domain\|null, Illuminate\\Database\\Eloquent\\Model\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/LinkResource.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\BaseVisitsPieChart\:\:applyDateFilter\(\) has parameter \$query with generic class Illuminate\\Database\\Eloquent\\Builder but does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Links/Widgets/BaseVisitsPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\BaseVisitsPieChart\:\:applyDateFilter\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Links/Widgets/BaseVisitsPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\BaseVisitsPieChart\:\:generateColorPalette\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Links/Widgets/BaseVisitsPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\BaseVisitsPieChart\:\:getEmptyStateData\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Links/Widgets/BaseVisitsPieChart.php

		-
			message: '#^Binary operation "\+\=" between \(float\|int\) and mixed results in an error\.$#'
			identifier: assignOp.invalid
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Cannot access property \$browser on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Cannot access property \$id on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Cannot access property \$total on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByBrowserPieChart\:\:getBrowserColors\(\) has parameter \$browsers with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByBrowserPieChart\:\:getBrowserColors\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByBrowserPieChart\:\:getData\(\) should return array\<string, mixed\> but returns array\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByBrowserPieChart\:\:normalizeBrowserNames\(\) has parameter \$data with generic class Illuminate\\Support\\Collection but does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByBrowserPieChart\:\:normalizeBrowserNames\(\) return type with generic class Illuminate\\Support\\Collection does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Parameter \#1 \$limit of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:take\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Parameter \#1 \$offset of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:slice\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Parameter \#2 \$subject of function preg_match expects string, mixed given\.$#'
			identifier: argument.type
			count: 9
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Variable \$totals might not be defined\.$#'
			identifier: variable.undefined
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByBrowserPieChart.php

		-
			message: '#^Cannot access property \$id on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByCountryPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByCountryPieChart\:\:getData\(\) should return array\<string, mixed\> but returns array\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByCountryPieChart.php

		-
			message: '#^Parameter \#1 \$limit of method Illuminate\\Support\\Collection\<int,Illuminate\\Database\\Eloquent\\Model\>\:\:take\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByCountryPieChart.php

		-
			message: '#^Parameter \#1 \$offset of method Illuminate\\Support\\Collection\<int,Illuminate\\Database\\Eloquent\\Model\>\:\:slice\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByCountryPieChart.php

		-
			message: '#^Parameter \#1 \.\.\.\$values of method Illuminate\\Support\\Collection\<int,Illuminate\\Database\\Eloquent\\Model\>\:\:push\(\) expects Illuminate\\Database\\Eloquent\\Model, object\{country\: string, total\: mixed\}&stdClass given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByCountryPieChart.php

		-
			message: '#^Binary operation "\+\=" between \(float\|int\) and mixed results in an error\.$#'
			identifier: assignOp.invalid
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Cannot access property \$id on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Cannot access property \$platform on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Cannot access property \$total on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByPlatformPieChart\:\:getData\(\) should return array\<string, mixed\> but returns array\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByPlatformPieChart\:\:getPlatformColors\(\) has parameter \$platforms with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByPlatformPieChart\:\:getPlatformColors\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByPlatformPieChart\:\:normalizePlatformNames\(\) has parameter \$data with generic class Illuminate\\Support\\Collection but does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsByPlatformPieChart\:\:normalizePlatformNames\(\) return type with generic class Illuminate\\Support\\Collection does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Parameter \#1 \$limit of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:take\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Parameter \#1 \$offset of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:slice\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Parameter \#2 \$subject of function preg_match expects string, mixed given\.$#'
			identifier: argument.type
			count: 8
			path: app/Filament/Resources/Links/Widgets/VisitsByPlatformPieChart.php

		-
			message: '#^Cannot access property \$id on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: property.nonObject
			count: 2
			path: app/Filament/Resources/Links/Widgets/VisitsCountChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsCountChart\:\:getDateRange\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsCountChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Links\\Widgets\\VisitsCountChart\:\:getOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsCountChart.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:map\(\) expects callable\(mixed, int\|string\)\: mixed, Closure\(Flowframe\\Trend\\TrendValue\)\: mixed given\.$#'
			identifier: argument.type
			count: 2
			path: app/Filament/Resources/Links/Widgets/VisitsCountChart.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:map\(\) expects callable\(mixed, int\|string\)\: string, Closure\(Flowframe\\Trend\\TrendValue\)\: string given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Links/Widgets/VisitsCountChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\BaseTagVisitsPieChart\:\:applyDateFilter\(\) has parameter \$query with generic class Illuminate\\Database\\Eloquent\\Builder but does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Tags/Widgets/BaseTagVisitsPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\BaseTagVisitsPieChart\:\:applyDateFilter\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Tags/Widgets/BaseTagVisitsPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\BaseTagVisitsPieChart\:\:generateColorPalette\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Tags/Widgets/BaseTagVisitsPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\BaseTagVisitsPieChart\:\:getEmptyStateData\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Tags/Widgets/BaseTagVisitsPieChart.php

		-
			message: '#^Binary operation "\+\=" between \(float\|int\) and mixed results in an error\.$#'
			identifier: assignOp.invalid
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Cannot access property \$browser on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Cannot access property \$total on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Cannot call method links\(\) on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Cannot call method select\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByBrowserPieChart\:\:getBrowserColors\(\) has parameter \$browsers with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByBrowserPieChart\:\:getBrowserColors\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByBrowserPieChart\:\:getData\(\) should return array\<string, mixed\> but returns array\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByBrowserPieChart\:\:normalizeBrowserNames\(\) has parameter \$data with generic class Illuminate\\Support\\Collection but does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByBrowserPieChart\:\:normalizeBrowserNames\(\) return type with generic class Illuminate\\Support\\Collection does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Parameter \#1 \$limit of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:take\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Parameter \#1 \$offset of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:slice\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Parameter \#2 \$subject of function preg_match expects string, mixed given\.$#'
			identifier: argument.type
			count: 9
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByBrowserPieChart.php

		-
			message: '#^Cannot call method links\(\) on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByCountryPieChart.php

		-
			message: '#^Cannot call method select\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByCountryPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByCountryPieChart\:\:getData\(\) should return array\<string, mixed\> but returns array\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByCountryPieChart.php

		-
			message: '#^Parameter \#1 \$limit of method Illuminate\\Support\\Collection\<int,Illuminate\\Database\\Eloquent\\Model\>\:\:take\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByCountryPieChart.php

		-
			message: '#^Parameter \#1 \$offset of method Illuminate\\Support\\Collection\<int,Illuminate\\Database\\Eloquent\\Model\>\:\:slice\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByCountryPieChart.php

		-
			message: '#^Parameter \#1 \.\.\.\$values of method Illuminate\\Support\\Collection\<int,Illuminate\\Database\\Eloquent\\Model\>\:\:push\(\) expects Illuminate\\Database\\Eloquent\\Model, object\{country\: string, total\: mixed\}&stdClass given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByCountryPieChart.php

		-
			message: '#^Binary operation "\+\=" between \(float\|int\) and mixed results in an error\.$#'
			identifier: assignOp.invalid
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Cannot access property \$platform on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Cannot access property \$total on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Cannot call method links\(\) on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Cannot call method select\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByPlatformPieChart\:\:getData\(\) should return array\<string, mixed\> but returns array\.$#'
			identifier: return.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByPlatformPieChart\:\:getPlatformColors\(\) has parameter \$platforms with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByPlatformPieChart\:\:getPlatformColors\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByPlatformPieChart\:\:normalizePlatformNames\(\) has parameter \$data with generic class Illuminate\\Support\\Collection but does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsByPlatformPieChart\:\:normalizePlatformNames\(\) return type with generic class Illuminate\\Support\\Collection does not specify its types\: TKey, TValue$#'
			identifier: missingType.generics
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Parameter \#1 \$limit of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:take\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Parameter \#1 \$offset of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:slice\(\) expects int, int\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Parameter \#2 \$subject of function preg_match expects string, mixed given\.$#'
			identifier: argument.type
			count: 8
			path: app/Filament/Resources/Tags/Widgets/TagVisitsByPlatformPieChart.php

		-
			message: '#^Cannot call method links\(\) on Illuminate\\Database\\Eloquent\\Model\|int\|string\|null\.$#'
			identifier: method.nonObject
			count: 2
			path: app/Filament/Resources/Tags/Widgets/TagVisitsCountChart.php

		-
			message: '#^Cannot call method select\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 2
			path: app/Filament/Resources/Tags/Widgets/TagVisitsCountChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsCountChart\:\:getDateRange\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsCountChart.php

		-
			message: '#^Method App\\Filament\\Resources\\Tags\\Widgets\\TagVisitsCountChart\:\:getOptions\(\) return type has no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsCountChart.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:map\(\) expects callable\(mixed, int\|string\)\: mixed, Closure\(Flowframe\\Trend\\TrendValue\)\: mixed given\.$#'
			identifier: argument.type
			count: 2
			path: app/Filament/Resources/Tags/Widgets/TagVisitsCountChart.php

		-
			message: '#^Parameter \#1 \$callback of method Illuminate\\Support\\Collection\<\(int\|string\),mixed\>\:\:map\(\) expects callable\(mixed, int\|string\)\: string, Closure\(Flowframe\\Trend\\TrendValue\)\: string given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Tags/Widgets/TagVisitsCountChart.php

		-
			message: '#^Parameter \#1 \$value of static method Illuminate\\Support\\Facades\\Hash\:\:make\(\) expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Filament/Resources/Users/<USER>

		-
			message: '#^Anonymous function never returns null so it can be removed from the return type\.$#'
			identifier: return.unusedType
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Anonymous function should return string but returns mixed\.$#'
			identifier: return.type
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Anonymous function should return string\|null but returns mixed\.$#'
			identifier: return.type
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Call to an undefined method Illuminate\\Database\\Eloquent\\Model\:\:activities\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot access offset ''attributes'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 6
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot access offset ''old'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 6
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot access offset \(int\|string\) on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 2
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot access property \$causer on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot access property \$changes on mixed\.$#'
			identifier: property.nonObject
			count: 8
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot access property \$created_at on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot access property \$event on mixed\.$#'
			identifier: property.nonObject
			count: 5
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot access property \$name on mixed\.$#'
			identifier: property.nonObject
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot call method format\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot call method latest\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Cannot call method with\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Method App\\History\\RecordHistory\:\:getChangesSummary\(\) has parameter \$record with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Method App\\History\\RecordHistory\:\:getChangesSummaryText\(\) has parameter \$record with no type specified\.$#'
			identifier: missingType.parameter
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Method App\\History\\RecordHistory\:\:getRelationship\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Method App\\History\\RecordHistory\:\:getRelationship\(\) return type with generic class Illuminate\\Database\\Eloquent\\Relations\\Relation does not specify its types\: TRelatedModel, TDeclaringModel, TResult$#'
			identifier: missingType.generics
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Method App\\History\\RecordHistory\:\:getRelationship\(\) should return Illuminate\\Database\\Eloquent\\Builder\|Illuminate\\Database\\Eloquent\\Relations\\Relation but returns mixed\.$#'
			identifier: return.type
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Parameter \#1 \$string of function ucfirst expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Parameter \#1 \$value of function collect expects Illuminate\\Contracts\\Support\\Arrayable\<\(int\|string\), mixed\>\|iterable\<\(int\|string\), mixed\>\|null, mixed given\.$#'
			identifier: argument.type
			count: 2
			path: app/History/RecordHistory.php

		-
			message: '#^Part \$newValue \(mixed\) of encapsed string cannot be cast to string\.$#'
			identifier: encapsedStringPart.nonString
			count: 2
			path: app/History/RecordHistory.php

		-
			message: '#^Part \$oldValue \(mixed\) of encapsed string cannot be cast to string\.$#'
			identifier: encapsedStringPart.nonString
			count: 2
			path: app/History/RecordHistory.php

		-
			message: '#^Unable to resolve the template type TKey in call to function collect$#'
			identifier: argument.templateType
			count: 2
			path: app/History/RecordHistory.php

		-
			message: '#^Unable to resolve the template type TValue in call to function collect$#'
			identifier: argument.templateType
			count: 2
			path: app/History/RecordHistory.php

		-
			message: '#^Using nullsafe property access "\?\-\>name" on left side of \?\? is unnecessary\. Use \-\> instead\.$#'
			identifier: nullsafe.neverNull
			count: 1
			path: app/History/RecordHistory.php

		-
			message: '#^Method App\\Http\\Middleware\\EnsureAdminPanelAccessible\:\:handle\(\) should return Symfony\\Component\\HttpFoundation\\Response but returns mixed\.$#'
			identifier: return.type
			count: 2
			path: app/Http/Middleware/EnsureAdminPanelAccessible.php

		-
			message: '#^Cannot access offset ''country'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 1
			path: app/Jobs/SaveVisitJob.php

		-
			message: '#^Cannot access offset ''iso_code'' on mixed\.$#'
			identifier: offsetAccess.nonOffsetAccessible
			count: 1
			path: app/Jobs/SaveVisitJob.php

		-
			message: '#^Cannot call method get\(\) on mixed\.$#'
			identifier: method.nonObject
			count: 1
			path: app/Jobs/SaveVisitJob.php

		-
			message: '#^Method App\\Jobs\\SaveVisitJob\:\:__construct\(\) has parameter \$request with no value type specified in iterable type array\.$#'
			identifier: missingType.iterableValue
			count: 1
			path: app/Jobs/SaveVisitJob.php

		-
			message: '#^Parameter \#1 \$ipAddress of method MaxMind\\Db\\Reader\:\:get\(\) expects string, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Jobs/SaveVisitJob.php

		-
			message: '#^Parameter \#1 \$u_agent of method donatj\\UserAgent\\UserAgentParser\:\:parse\(\) expects string\|null, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Jobs/SaveVisitJob.php

		-
			message: '#^Parameter \#1 \$body of method Filament\\Notifications\\Notification\:\:body\(\) expects Closure\|string\|null, array\|string\|null given\.$#'
			identifier: argument.type
			count: 1
			path: app/Livewire/LinkPage.php

		-
			message: '#^Parameter \#2 \$array of function array_key_exists expects array, array\|string given\.$#'
			identifier: argument.type
			count: 1
			path: app/Livewire/LinkPage.php

		-
			message: '#^Parameter \#2 \$replace of function __ expects array\<string, bool\|float\|int\|string\>, array\<string, mixed\> given\.$#'
			identifier: argument.type
			count: 1
			path: app/Livewire/LinkPage.php

		-
			message: '#^Class App\\Models\\Domain uses generic trait Illuminate\\Database\\Eloquent\\Factories\\HasFactory but does not specify its types\: TFactory$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Domain.php

		-
			message: '#^Method App\\Models\\Domain\:\:adminPanelAvailable\(\) has parameter \$query with generic class Illuminate\\Database\\Eloquent\\Builder but does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Domain.php

		-
			message: '#^Method App\\Models\\Domain\:\:adminPanelAvailable\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Domain.php

		-
			message: '#^Method App\\Models\\Domain\:\:available\(\) has parameter \$query with generic class Illuminate\\Database\\Eloquent\\Builder but does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Domain.php

		-
			message: '#^Method App\\Models\\Domain\:\:available\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Domain.php

		-
			message: '#^Method App\\Models\\Domain\:\:links\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Link, Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Link, \$this\(App\\Models\\Domain\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Domain.php

		-
			message: '#^Type string in generic type Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Link, Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\> in PHPDoc tag @return is not subtype of template type TPivotModel of Illuminate\\Database\\Eloquent\\Relations\\Pivot \= Illuminate\\Database\\Eloquent\\Relations\\Pivot of class Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\.$#'
			identifier: generics.notSubtype
			count: 1
			path: app/Models/Domain.php

		-
			message: '#^Class App\\Models\\Link uses generic trait Illuminate\\Database\\Eloquent\\Factories\\HasFactory but does not specify its types\: TFactory$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Visit\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasMany\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:available\(\) has parameter \$query with generic class Illuminate\\Database\\Eloquent\\Builder but does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:available\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:domains\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Domain, Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Domain, \$this\(App\\Models\\Link\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:forCurrentDomain\(\) has parameter \$query with generic class Illuminate\\Database\\Eloquent\\Builder but does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:forCurrentDomain\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:passwordProtected\(\) has parameter \$query with generic class Illuminate\\Database\\Eloquent\\Builder but does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:passwordProtected\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:tags\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Tag, Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Tag, \$this\(App\\Models\\Link\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Method App\\Models\\Link\:\:visits\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Visit\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasMany\<App\\Models\\Visit, \$this\(App\\Models\\Link\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Type string in generic type Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Domain, Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\> in PHPDoc tag @return is not subtype of template type TPivotModel of Illuminate\\Database\\Eloquent\\Relations\\Pivot \= Illuminate\\Database\\Eloquent\\Relations\\Pivot of class Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\.$#'
			identifier: generics.notSubtype
			count: 2
			path: app/Models/Link.php

		-
			message: '#^Type string in generic type Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Tag, Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\> in PHPDoc tag @return is not subtype of template type TPivotModel of Illuminate\\Database\\Eloquent\\Relations\\Pivot \= Illuminate\\Database\\Eloquent\\Relations\\Pivot of class Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\.$#'
			identifier: generics.notSubtype
			count: 1
			path: app/Models/Link.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Domain\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/LinkDomain.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Link\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/LinkDomain.php

		-
			message: '#^Method App\\Models\\LinkDomain\:\:domain\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Domain\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Domain, \$this\(App\\Models\\LinkDomain\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/LinkDomain.php

		-
			message: '#^Method App\\Models\\LinkDomain\:\:link\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Link\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Link, \$this\(App\\Models\\LinkDomain\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/LinkDomain.php

		-
			message: '#^Class App\\Models\\Tag uses generic trait Illuminate\\Database\\Eloquent\\Factories\\HasFactory but does not specify its types\: TFactory$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Tag.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\HasManyThrough\<App\\Models\\Visit\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\HasManyThrough\: TRelatedModel, TIntermediateModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/Tag.php

		-
			message: '#^Method App\\Models\\Tag\:\:links\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Link, Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Link, \$this\(App\\Models\\Tag\), Illuminate\\Database\\Eloquent\\Relations\\Pivot, ''pivot''\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Tag.php

		-
			message: '#^Method App\\Models\\Tag\:\:visits\(\) should return Illuminate\\Database\\Eloquent\\Relations\\HasManyThrough\<App\\Models\\Visit\> but returns Illuminate\\Database\\Eloquent\\Relations\\HasManyThrough\<App\\Models\\Visit, App\\Models\\LinkTag, \$this\(App\\Models\\Tag\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Tag.php

		-
			message: '#^Type string in generic type Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\<App\\Models\\Link, Illuminate\\Database\\Eloquent\\Relations\\Pivot, string\> in PHPDoc tag @return is not subtype of template type TPivotModel of Illuminate\\Database\\Eloquent\\Relations\\Pivot \= Illuminate\\Database\\Eloquent\\Relations\\Pivot of class Illuminate\\Database\\Eloquent\\Relations\\BelongsToMany\.$#'
			identifier: generics.notSubtype
			count: 1
			path: app/Models/Tag.php

		-
			message: '#^Method App\\Models\\User\:\:active\(\) has parameter \$query with generic class Illuminate\\Database\\Eloquent\\Builder but does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/User.php

		-
			message: '#^Method App\\Models\\User\:\:active\(\) return type with generic class Illuminate\\Database\\Eloquent\\Builder does not specify its types\: TModel$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/User.php

		-
			message: '#^PHPDoc type array\<int, string\> of property App\\Models\\User\:\:\$fillable is not covariant with PHPDoc type list\<string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$fillable\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/User.php

		-
			message: '#^PHPDoc type array\<int, string\> of property App\\Models\\User\:\:\$hidden is not covariant with PHPDoc type list\<string\> of overridden property Illuminate\\Database\\Eloquent\\Model\:\:\$hidden\.$#'
			identifier: property.phpDocType
			count: 1
			path: app/Models/User.php

		-
			message: '#^Class App\\Models\\Visit uses generic trait Illuminate\\Database\\Eloquent\\Factories\\HasFactory but does not specify its types\: TFactory$#'
			identifier: missingType.generics
			count: 1
			path: app/Models/Visit.php

		-
			message: '#^Generic type Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Link\> in PHPDoc tag @return does not specify all template types of class Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\: TRelatedModel, TDeclaringModel$#'
			identifier: generics.lessTypes
			count: 1
			path: app/Models/Visit.php

		-
			message: '#^Method App\\Models\\Visit\:\:link\(\) should return Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Link\> but returns Illuminate\\Database\\Eloquent\\Relations\\BelongsTo\<App\\Models\\Link, \$this\(App\\Models\\Visit\)\>\.$#'
			identifier: return.type
			count: 1
			path: app/Models/Visit.php

		-
			message: '#^Call to an undefined method App\\Providers\\Filament\\AdminPanelProvider\:\:formatStateUsing\(\)\.$#'
			identifier: method.notFound
			count: 1
			path: app/Providers/Filament/AdminPanelProvider.php

		-
			message: '#^Parameter \#1 \$number of static method Illuminate\\Support\\Number\:\:abbreviate\(\) expects float\|int, mixed given\.$#'
			identifier: argument.type
			count: 1
			path: app/Providers/Filament/AdminPanelProvider.php
